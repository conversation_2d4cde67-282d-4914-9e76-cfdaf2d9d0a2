<template>
  <!-- ✅ Optimized Modal Overlay with Teleport -->
  <Teleport to="body">
    <Transition name="modal" appear>
      <div
        v-if="isVisible"
        class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
        @click.self="handleBackdropClick"
        role="dialog"
        aria-modal="true"
        aria-labelledby="ffm-dialog-title"
        aria-describedby="ffm-dialog-description"
      >
        <!-- ✅ Optimized Modal Content with CSS containment -->
        <div
          class="bg-secondary rounded-lg shadow-xl max-w-6xl w-full h-[80vh] overflow-hidden"
          style="contain: layout style paint"
          @click.stop
        >
          <!-- ✅ Enhanced Header with better accessibility -->
          <header
            class="flex items-center justify-between border-b border-gray-200 bg-white"
          >
            <div class="w-8"></div>
            <!-- Spacer for centering -->
            <h1
              id="ffm-dialog-title"
              class="text-lg font-semibold text-gray-900"
            >
              Xử lí FFM đơn hàng
            </h1>
            <button
              @click="cancel"
              class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors duration-200 "
              aria-label="Đóng dialog"
              type="button"
            >
              <svg
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </header>

          <!-- ✅ Enhanced Mobile Content with better scrolling -->
          <div
            class="block md:hidden max-h-[calc(80vh-80px)] overflow-y-auto space-y-2"
            style="scrollbar-width: thin; scrollbar-color: #d1d5db #f3f4f6"
          >
            <!-- Loading State -->
            <div v-if="loading" class="space-y-4">
              <div class="animate-pulse space-y-4">
                <div class="h-32 bg-gray-200 rounded-lg"></div>
                <div class="h-24 bg-gray-200 rounded-lg"></div>
                <div class="h-40 bg-gray-200 rounded-lg"></div>
              </div>
            </div>

            <!-- Content -->
            <template v-else>
              <Suspense>
                <InfoOrderFFM :order="order" :dataFFM="dataFFM" />
                <template #fallback>
                  <div class="h-32 bg-gray-100 animate-pulse rounded-lg"></div>
                </template>
              </Suspense>

              <Suspense>
                <ExportWarehouse
                  v-if="dataFFM"
                  :order="order"
                  :dataFFM="dataFFM"
                  @fetchFFMStatus="handleGetFFMStage"
                />
                <template #fallback>
                  <div class="h-24 bg-gray-100 animate-pulse rounded-lg"></div>
                </template>
              </Suspense>

              <Suspense>
                <WrapProduct
                  v-if="shouldShowWrapProduct"
                  :dataPackageBox="dataPackageBox"
                  :order="order"
                  :dataFFM="dataFFM"
                  @fetchFFMStatus="handleGetFFMStage"
                />
                <template #fallback>
                  <div class="h-40 bg-gray-100 animate-pulse rounded-lg"></div>
                </template>
              </Suspense>

              <Suspense>
                <LinkShipping
                  v-if="order?.order?.shippingAddress"
                  :order="order"
                  :dataFFM="dataFFM"
                  :shippingAddress="orderStore.dataDefaultAddress"
                  :customer="orderStore.customerInOrder"
                  @fetchFFMStatus="handleGetFFMStage"
                />
                <template #fallback>
                  <div class="h-32 bg-gray-100 animate-pulse rounded-lg"></div>
                </template>
              </Suspense>
            </template>
          </div>

          <!-- ✅ Enhanced Desktop Content -->
          <div class="hidden md:block">
            <div class="grid grid-cols-12 gap-2 p-2">
              <!-- Left Column - Process Steps -->
              <div class="col-span-8 space-y-4">
                <!-- ✅ Enhanced Progress Bar -->
                <div
                  v-if="order.order.fulfillmentStatus !== 'UNFULFILLED'"
                  class="bg-white rounded-lg p-2 border border-gray-200 shadow-sm"
                >
                  <h2 class="text-lg font-semibold text-gray-900 mb-4">
                    Tiến trình xử lý
                  </h2>
                  <div class="flex items-center justify-between relative">
                    <template v-for="(step, index) in steps" :key="index">
                      <div class="flex flex-col items-center flex-1 relative">
                        <!-- Progress Circle -->
                        <div
                          class="w-8 h-8 rounded-full flex items-center justify-center z-10 transition-all duration-300"
                          :class="{
                            'bg-primary text-white shadow-lg': index < indexCSS,
                            'border-2 border-primary bg-white text-primary':
                              index === indexCSS,
                            'bg-gray-200 text-gray-400': index > indexCSS,
                          }"
                        >
                          <svg
                            v-if="index < indexCSS"
                            class="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clip-rule="evenodd"
                            />
                          </svg>
                          <span v-else class="text-sm font-medium">{{
                            index + 1
                          }}</span>
                        </div>

                        <!-- Step Label -->
                        <div
                          class="mt-3 text-sm text-center font-medium transition-colors duration-300"
                          :class="{
                            'text-primary': index <= indexCSS,
                            'text-gray-400': index > indexCSS,
                          }"
                        >
                          {{ step }}
                        </div>

                        <!-- Progress Line -->
                        <div
                          v-if="index < steps.length - 1"
                          class="absolute top-4 left-1/2 w-full h-0.5 -translate-y-1/2"
                        >
                          <div
                            class="h-full transition-all duration-500"
                            :class="{
                              'bg-primary': index < indexCSS,
                              'bg-gray-200': index >= indexCSS,
                            }"
                          ></div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>

                <!-- ✅ Process Steps Content with Loading States -->
                <div
                  class="space-y-4 max-h-[calc(80vh-200px)] overflow-y-auto"
                  style="scrollbar-width: thin"
                >
                  <!-- Loading State -->
                  <div v-if="loading" class="space-y-6">
                    <div class="animate-pulse space-y-4">
                      <div class="h-32 bg-gray-200 rounded-lg"></div>
                      <div class="h-24 bg-gray-200 rounded-lg"></div>
                      <div class="h-40 bg-gray-200 rounded-lg"></div>
                    </div>
                  </div>

                  <!-- Content -->
                  <template v-else>
                    <Suspense>
                      <ExportWarehouse
                        v-if="dataFFM"
                        :order="order"
                        :dataFFM="dataFFM"
                        @fetchFFMStatus="handleGetFFMStage"
                      />
                      <template #fallback>
                        <div
                          class="h-32 bg-gray-100 animate-pulse rounded-lg"
                        ></div>
                      </template>
                    </Suspense>

                    <Suspense>
                      <WrapProduct
                        v-if="shouldShowWrapProduct"
                        :dataPackageBox="dataPackageBox"
                        :order="order"
                        :dataFFM="dataFFM"
                        @fetchFFMStatus="handleGetFFMStage"
                      />
                      <template #fallback>
                        <div
                          class="h-40 bg-gray-100 animate-pulse rounded-lg"
                        ></div>
                      </template>
                    </Suspense>

                    <Suspense>
                      <LinkShipping
                        v-if="order?.order?.shippingAddress"
                        :order="order"
                        :dataFFM="dataFFM"
                        :shippingAddress="orderStore.dataDefaultAddress"
                        :customer="orderStore.customerInOrder"
                        @fetchFFMStatus="handleGetFFMStage"
                      />
                      <template #fallback>
                        <div
                          class="h-32 bg-gray-100 animate-pulse rounded-lg"
                        ></div>
                      </template>
                    </Suspense>
                  </template>
                </div>
              </div>

              <!-- Right Column - Order Info -->
              <div class="col-span-4">
                <div>
                  <Suspense>
                    <InfoOrderFFM :order="order" :dataFFM="dataFFM" />
                    <template #fallback>
                      <div
                        class="h-96 bg-gray-100 animate-pulse rounded-lg"
                      ></div>
                    </template>
                  </Suspense>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
// ✅ Enhanced TypeScript Interfaces
interface Order {
  id: string;
  order: {
    fulfillmentStatus: string;
    shippingAddress?: any;
  };
}

interface Props {
  order: Order;
}

// ✅ Props and Emits with TypeScript
const props = defineProps<Props>();
const emit = defineEmits<{
  confirm: [];
  cancel: [];
}>();

// ✅ Reactive State with proper typing
const isVisible = ref(true);
const loading = ref(false);
const dataFFM = ref<any>(null);
const dataPackageBox = ref<any[]>([]);
const styleCSS = ref("");
const indexCSS = ref(0);

// ✅ Memoized computed properties
const steps = computed(() => {
  return props.order?.order?.shippingAddress
    ? ["Xuất kho", "Đóng gói", "Vận chuyển", "Hoàn thành"]
    : ["Xuất kho", "Hoàn thành"];
});

const shouldShowWrapProduct = computed(() => {
  return (
    dataPackageBox.value.length > 0 &&
    props.order?.order?.shippingAddress &&
    dataFFM.value
  );
});

// ✅ Enhanced Event Handlers
const cancel = () => {
  emit("cancel");
  isVisible.value = false;
};

const handleBackdropClick = () => {
  // Optional: Allow closing on backdrop click
  // cancel();
};

// ✅ Lazy Loading Components for Better Performance
const InfoOrderFFM = defineAsyncComponent({
  loader: () => import("~/components/Order/FFM/InfoOrderFFM.vue"),
  delay: 200,
  timeout: 3000,
  loadingComponent: () =>
    h("div", { class: "h-32 bg-gray-100 animate-pulse rounded-lg" }),
  errorComponent: () =>
    h("div", { class: "p-4 text-red-500" }, "Failed to load component"),
});

const ExportWarehouse = defineAsyncComponent({
  loader: () => import("~/components/Order/FFM/ExportWarehouse.vue"),
  delay: 200,
  timeout: 3000,
  loadingComponent: () =>
    h("div", { class: "h-24 bg-gray-100 animate-pulse rounded-lg" }),
  errorComponent: () =>
    h("div", { class: "p-4 text-red-500" }, "Failed to load component"),
});

const WrapProduct = defineAsyncComponent({
  loader: () => import("~/components/Order/FFM/WrapProduct.vue"),
  delay: 200,
  timeout: 3000,
  loadingComponent: () =>
    h("div", { class: "h-40 bg-gray-100 animate-pulse rounded-lg" }),
  errorComponent: () =>
    h("div", { class: "p-4 text-red-500" }, "Failed to load component"),
});

const LinkShipping = defineAsyncComponent({
  loader: () => import("~/components/Order/FFM/LinkShipping.vue"),
  delay: 200,
  timeout: 3000,
  loadingComponent: () =>
    h("div", { class: "h-32 bg-gray-100 animate-pulse rounded-lg" }),
  errorComponent: () =>
    h("div", { class: "p-4 text-red-500" }, "Failed to load component"),
});

// ✅ Composables
const { getPackageBox, ffmStage, shipmentParameter } = usePortal();
const orderStore = useOrderStore();

// ✅ Enhanced Package Box Handler with Error Handling
const handleGetPackageBox = async () => {
  try {
    const response = await getPackageBox();
    const defaultOption = {
      id: "1",
      name: "Chọn kích thước đóng gói",
      dimension: {
        length: "0",
        width: "0",
        height: "0",
        dimensionUnit: "CM",
      },
      weight: "0",
      weightUnit: "KILOGRAMS",
    };

    dataPackageBox.value = response?.data
      ? [defaultOption, ...response.data]
      : [defaultOption];
  } catch (error) {
    console.error("Error fetching package box:", error);
    // Fallback to default option only
    dataPackageBox.value = [
      {
        id: "1",
        name: "Chọn kích thước đóng gói",
        dimension: {
          length: "0",
          width: "0",
          height: "0",
          dimensionUnit: "CM",
        },
        weight: "0",
        weightUnit: "KILOGRAMS",
      },
    ];
  }
};

// ✅ Enhanced FFM Stage Handler with Error Handling
const handleGetFFMStage = async () => {
  try {
    const response = await ffmStage(props.order?.id);
    dataFFM.value = response?.data;

    // Use utility function to calculate stage
    const { calculateFFMStage } = await import("~/utils/ffmHelpers");
    const stageResult = calculateFFMStage(dataFFM.value);
    styleCSS.value = stageResult.styleCSS;
    indexCSS.value = stageResult.indexCSS;
  } catch (error) {
    console.error("Error fetching FFM stage:", error);
    // Reset to default state on error
    styleCSS.value = "";
    indexCSS.value = 0;
  }
};

// ✅ Enhanced Shipment Parameter Handler
const handleShipmentParameter = async () => {
  try {
    await shipmentParameter(props.order?.id);
  } catch (error) {
    console.error("Error fetching shipment parameter:", error);
  }
};

// ✅ Enhanced Lifecycle with Better Error Handling
onMounted(async () => {
  loading.value = true;

  try {
    await Promise.allSettled([
      handleGetFFMStage(),
      handleGetPackageBox(),
      handleShipmentParameter(),
      orderStore.updateOrder(props.order?.id),
    ]);
  } catch (error) {
    console.error("Error during component initialization:", error);
  } finally {
    loading.value = false;
  }
});

// ✅ Cleanup on unmount
onUnmounted(() => {
  // Clean up any pending operations if needed
});
</script>

<style scoped>
/* ✅ Enhanced Modal Animations */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.modal-enter-to,
.modal-leave-from {
  opacity: 1;
  transform: scale(1);
}

/* ✅ Custom Scrollbar Styles */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* ✅ Enhanced Loading Animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* ✅ Focus States for Accessibility */
button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* ✅ Mobile Optimizations */
@media (max-width: 768px) {
  .space-y-4 > * + * {
    margin-top: 1rem;
  }

  .space-y-6 > * + * {
    margin-top: 1.5rem;
  }

  /* Mobile modal adjustments */
  .modal-content {
    margin: 1rem;
    max-height: 95vh;
    min-height: 70vh;
  }

  /* Mobile scrolling optimization */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}

/* ✅ CSS Containment for Performance */
.modal-content {
  contain: layout style paint;
}

/* ✅ Backdrop Blur Support */
@supports (backdrop-filter: blur(4px)) {
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }
}

/* ✅ Progress Bar Enhancements */
.progress-line {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-circle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ✅ Hover Effects */
.hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
