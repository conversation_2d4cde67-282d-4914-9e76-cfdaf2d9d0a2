<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center gap-3">
        <div
          class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-primary"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <div>
          <h2 class="text-lg font-semibold text-gray-900">
            Thông tin chung sản phẩm
          </h2>
          <p class="text-sm text-gray-500">
            Quản lý thông tin cơ bản của sản phẩm
          </p>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-2 space-y-2">
      <!-- Product ID & SKU -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Mã sản phẩm
            <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            v-model="id"
            readonly
            class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 focus:outline-none cursor-not-allowed"
            placeholder="Tự động tạo"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            SKU
          </label>
          <input
            type="text"
            readonly
            v-model="sku"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 focus:outline-none cursor-not-allowed"
            placeholder="Nhập mã SKU"
          />
        </div>
      </div>

      <!-- Product Name -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Tên sản phẩm
          <span class="text-red-500">*</span>
        </label>
        <input
          type="text"
          v-model="title"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
          placeholder="Nhập tên sản phẩm"
          @blur="handleUpdateProductTitle"
        />
      </div>
      <!-- Categories -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Danh mục sản phẩm
        </label>

        <!-- Selected Categories -->
        <div class="mb-3">
          <div
            v-if="product?.categories?.length > 0"
            class="flex flex-wrap gap-2"
          >
            <span
              v-for="category in product.categories"
              :key="category.id"
              class="inline-flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
            >
              {{ category?.title }}
              <button
                @click="handleRemoveCategory(category.id)"
                class="text-blue-600 hover:text-red-600 transition-colors duration-200"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </span>
          </div>
          <p v-else class="text-sm text-gray-500 italic">
            Chưa có danh mục nào được chọn
          </p>
        </div>

        <!-- Add Category -->
        <div class="flex gap-2">
          <select
            v-model="selectedCategory"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="" disabled>Chọn danh mục để thêm</option>
            <option
              v-for="category in availableCategories"
              :key="category?.id"
              :value="category?.id"
            >
              {{ category?.title }}
            </option>
          </select>
          <button
            @click="handleChangeCategory"
            :disabled="!selectedCategory"
            class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            Thêm
          </button>
        </div>
      </div>

      <!-- Short Description -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Mô tả ngắn
        </label>
        <textarea
          v-model="shortDescription"
          rows="4"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 resize-none"
          placeholder="Nhập mô tả ngắn về sản phẩm..."
          @blur="handleChangeShortDescription"
        ></textarea>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
interface Props {
  product: any;
}

const props = defineProps<Props>();
const emit = defineEmits(["update"]);

const id = ref<string>("");
const sku = ref<string>("");
const title = ref<string>("");
const shortDescription = ref<string>("");

// Watch for product changes
watch(
  () => props.product,
  (newVal) => {
    if (newVal) {
      id.value = newVal.id || "";
      sku.value = newVal.sku || "";
      title.value = newVal.title || "";
      shortDescription.value = newVal.shortDescription || "";
    }
  },
  { immediate: true }
);

const auth = useCookie("auth") as any;
const {
  updateProductTitle,
  getCategory,
  updateCategory,
  updateShortDescription,
} = useProduct();

const dataCategory = ref<any[]>([]);
const selectedCategory = ref<string>("");

// Computed property for available categories
const availableCategories = computed(() => {
  if (!props.product?.categories) return dataCategory.value;

  const selectedIds = props.product.categories.map((cat: any) => cat.id);
  return dataCategory.value.filter((cat: any) => !selectedIds.includes(cat.id));
});

// Load categories
const handleGetCategories = async () => {
  try {
    const response = await getCategory("", 1);
    dataCategory.value = response;
  } catch (error) {
    console.error("Error loading categories:", error);
  }
};

// Update handlers
const handleUpdateProductTitle = async () => {
  if (!title.value.trim()) return;

  try {
    await updateProductTitle(id.value, title.value, auth.value?.user?.id);
    emit("update");
  } catch (error) {
    console.error("Error updating product title:", error);
  }
};

const handleChangeCategory = async () => {
  if (!selectedCategory.value) return;

  try {
    await updateCategory(
      props.product?.id,
      selectedCategory.value,
      auth.value?.user?.id
    );
    selectedCategory.value = "";
    emit("update");
  } catch (error) {
    console.error("Error adding category:", error);
  }
};

const handleRemoveCategory = async (categoryId: string) => {
  try {
    await updateCategory(props.product?.id, categoryId, auth.value?.user?.id);
    emit("update");
  } catch (error) {
    console.error("Error removing category:", error);
  }
};

const handleChangeShortDescription = async () => {
  try {
    await updateShortDescription(
      props.product?.id,
      shortDescription.value,
      auth.value?.user?.id
    );
    emit("update");
  } catch (error) {
    console.error("Error updating short description:", error);
  }
};

onMounted(async () => {
  await handleGetCategories();
});
</script>
